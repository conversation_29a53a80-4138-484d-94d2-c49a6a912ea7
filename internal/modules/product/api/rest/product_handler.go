package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type ProductHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
	ValidateCode(w http.ResponseWriter, r *http.Request)
	GetProductsByCategoryCode(w http.ResponseWriter, r *http.Request)
}

type productHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.ProductUsecase
}

func NewProductHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.ProductUsecase,
) ProductHandler {
	return &productHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}

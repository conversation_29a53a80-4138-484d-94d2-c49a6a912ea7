package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/repo/pg"
)

type productRepository struct {
	pgRepo pg.ProductPostgreRepo
}

// CountByProp implements model.ProductRepository.
func (p *productRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return p.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.ProductRepository.
func (p *productRepository) Create(ctx context.Context, product model.Product) error {
	return p.pgRepo.Create(ctx, product)
}

// Delete implements model.ProductRepository.
func (p *productRepository) Delete(ctx context.Context, id string) error {
	return p.pgRepo.Delete(ctx, id)
}

// GetAll implements model.ProductRepository.
func (p *productRepository) GetAll(ctx context.Context) ([]model.Product, error) {
	return p.pgRepo.GetAll(ctx)
}

// GetByProp implements model.ProductRepository.
func (p *productRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Product, error) {
	return p.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.ProductRepository.
func (p *productRepository) Update(ctx context.Context, product model.Product) error {
	return p.pgRepo.Update(ctx, product)
}

// GetProductsByCategoryCode implements model.ProductRepository.
func (p *productRepository) GetProductsByCategoryCode(ctx context.Context, categoryCode string) ([]model.Product, error) {
	return p.pgRepo.GetProductsByCategoryCode(ctx, categoryCode)
}

func NewProductRepository(pgRepo pg.ProductPostgreRepo) model.ProductRepository {
	return &productRepository{
		pgRepo: pgRepo,
	}
}

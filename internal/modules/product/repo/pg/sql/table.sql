CREATE TABLE dev.products (
    id VARCHAR(255) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    image_url VARCHAR(500),
    commercial_name VARCHAR(255) NOT NULL,
    code VA<PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    sku_code VA<PERSON>HAR(255) NOT NULL UNIQUE,
    measurement_unit_id VARCHAR(255) NOT NULL,
    category_id VARCHAR(255) NOT NULL,
    brand_id VARCHAR(255) NOT NULL,
    state VARCHAR(50) NOT NULL DEFAULT 'active',
    description TEXT,
    can_be_sold BOOLEAN NOT NULL DEFAULT true,
    can_be_purchased BOOLEAN NOT NULL DEFAULT true,
    cost_price DECIMAL(10,2),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ,
    FOREIGN KEY (measurement_unit_id) REFERENCES dev.measurement_units(id),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (category_id) REFERENCES dev.categories(id),
    FOREIGN KEY (brand_id) REFERENCES dev.brands(id)
);

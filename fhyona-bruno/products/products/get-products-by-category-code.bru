meta {
  name: Get Products By Category Code
  type: http
  seq: 7
}

get {
  url: {{url}}/api/v1/products/category/ELECTRONICS
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return array of products", function() {
    expect(res.getBody()).to.be.an('array');
  });
  
  test("products should have required fields", function() {
    const products = res.getBody();
    if (products.length > 0) {
      expect(products[0]).to.have.property('id');
      expect(products[0]).to.have.property('name');
      expect(products[0]).to.have.property('code');
      expect(products[0]).to.have.property('category_id');
    }
  });
}

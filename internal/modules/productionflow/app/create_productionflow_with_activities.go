package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// CreateWithActivities implements model.ProductionFlowUsecase.
func (p *productionFlowUsecase) CreateWithActivities(ctx context.Context, request model.ProductionFlowCreateWithActivities) (string, error) {
	// Check if code already exists
	codeExists, err := p.repo.CountByProp(ctx, "code", request.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if production flow code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.ProductionFlowConflictCodef("Production flow code already exists", nil, nil)
	}

	// Check if name already exists
	nameExists, err := p.repo.CountByProp(ctx, "name", request.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if production flow name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.ProductionFlowConflictNamef("Production flow name already exists", nil, nil)
	}

	// Validate that index numbers are unique and sequential
	indexMap := make(map[int]bool)
	for _, activity := range request.Activities {
		if indexMap[activity.IndexNumber] {
			return "", utils.BadRequestf("Duplicate index number found in activities", nil, nil)
		}
		indexMap[activity.IndexNumber] = true
	}

	// Create the production flow
	productionFlowID := utils.UniqueId()
	newProductionFlow := model.ProductionFlow{
		ID:   productionFlowID,
		Code: request.Code,
		Name: request.Name,
	}

	// Create activities
	activities := make([]model.Activity, len(request.Activities))
	for i, activityReq := range request.Activities {
		activities[i] = model.Activity{
			ID:               utils.UniqueId(),
			ProductionFlowID: productionFlowID,
			WorkAreaID:       activityReq.WorkAreaID,
			OperationID:      activityReq.OperationID,
			IndexNumber:      activityReq.IndexNumber,
		}
	}

	// Create production flow with activities in a transaction
	err = p.repo.CreateWithActivities(ctx, newProductionFlow, activities)
	if err != nil {
		return "", err
	}

	return productionFlowID, nil
}

package model

import "time"

type Product struct {
	ID                string
	Name              string
	ImageURL          *string
	CommercialName    string
	Code              string
	SKUCode           string
	MeasurementUnitID string
	CategoryID        string
	BrandID           string
	State             string
	Description       *string
	CanBeSold         bool
	CanBePurchased    bool
	CostPrice         *float64
	CreatedAt         *time.Time
	UpdatedAt         *time.Time
	DeletedAt         *time.Time
}

type ProductCreate struct {
	Name              string
	ImageURL          *string
	CommercialName    string
	Code              string
	SKUCode           string
	MeasurementUnitID string
	CategoryID        string
	BrandID           string
	State             string
	Description       *string
	CanBeSold         bool
	CanBePurchased    bool
	CostPrice         *float64
}

type ProductUpdate struct {
	ID                string
	Name              string
	ImageURL          *string
	CommercialName    string
	Code              string
	SKUCode           string
	MeasurementUnitID string
	CategoryID        string
	BrandID           string
	State             string
	Description       *string
	CanBeSold         bool
	CanBePurchased    bool
	CostPrice         *float64
}

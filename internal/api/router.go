package api

import (
	"net/http"

	"github.com/sirupsen/logrus"

	authRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/auth/api/rest"
	brandRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/api/rest"
	categoryRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/api/rest"
	measurementUnitRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/api/rest"
	operationRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/operation/api/rest"
	productionFlowRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/api/rest"
	userRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/user/api/rest"
	workAreaRest "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/api/rest"

	restMdlwr "github.com/JosueDiazC/fhyona-v2-backend/internal/services/middleware/rest"
	restUtils "github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type RouteConfig interface {
	SetupRoutes()
}

type routeConfig struct {
	http                   *http.ServeMux
	log                    *logrus.Logger
	middlware              restMdlwr.HTTPMiddleware
	userHandler            userRest.UserHandler
	authHandler            authRest.AuthHandler
	brandHandler           brandRest.BrandHandler
	categoryHandler        categoryRest.CategoryHandler
	workAreaHandler        workAreaRest.WorkAreaHandler
	operationHandler       operationRest.OperationHandler
	productionFlowHandler  productionFlowRest.ProductionFlowHandler
	measurementUnitHandler measurementUnitRest.MeasurementUnitHandler
}

// SetupRoutes implements RouteConfig.
func (r *routeConfig) SetupRoutes() {
	routes := http.NewServeMux()
	baseMiddleware := restUtils.ComposeHMiddleware(
		r.middlware.Cors,
		r.middlware.CorrelationID,
		r.middlware.Logging,
		r.middlware.Client,
	)

	authMiddleware := restUtils.ComposeHFMiddleware(
		r.middlware.Authenticated,
	)

	r.http.Handle("/", baseMiddleware(routes))

	routes.HandleFunc("POST /api/v1/users", authMiddleware(r.userHandler.Create))
	routes.HandleFunc("PUT /api/v1/users", authMiddleware(r.userHandler.Update))
	routes.HandleFunc("GET /api/v1/users/{id}", authMiddleware(r.userHandler.GetById))
	routes.HandleFunc("GET /api/v1/users", authMiddleware(r.userHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/users/{id}", authMiddleware(r.userHandler.Delete))

	routes.HandleFunc("POST /api/v1/brands", authMiddleware(r.brandHandler.Create))
	routes.HandleFunc("PUT /api/v1/brands", authMiddleware(r.brandHandler.Update))
	routes.HandleFunc("GET /api/v1/brands/{id}", authMiddleware(r.brandHandler.GetById))
	routes.HandleFunc("GET /api/v1/brands", authMiddleware(r.brandHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/brands/{id}", authMiddleware(r.brandHandler.Delete))
	routes.HandleFunc("GET /api/v1/brands/validate-code/{code}", authMiddleware(r.brandHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/measurement-units", authMiddleware(r.measurementUnitHandler.Create))
	routes.HandleFunc("PUT /api/v1/measurement-units", authMiddleware(r.measurementUnitHandler.Update))
	routes.HandleFunc("GET /api/v1/measurement-units/{id}", authMiddleware(r.measurementUnitHandler.GetById))
	routes.HandleFunc("GET /api/v1/measurement-units", authMiddleware(r.measurementUnitHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/measurement-units/{id}", authMiddleware(r.measurementUnitHandler.Delete))
	routes.HandleFunc("GET /api/v1/measurement-units/validate-code/{code}", authMiddleware(r.measurementUnitHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/categories", authMiddleware(r.categoryHandler.Create))
	routes.HandleFunc("PUT /api/v1/categories", authMiddleware(r.categoryHandler.Update))
	routes.HandleFunc("GET /api/v1/categories/{id}", authMiddleware(r.categoryHandler.GetById))
	routes.HandleFunc("GET /api/v1/categories", authMiddleware(r.categoryHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/categories/{id}", authMiddleware(r.categoryHandler.Delete))
	routes.HandleFunc("GET /api/v1/categories/subcategories/{id}", authMiddleware(r.categoryHandler.GetSubcategories))
	routes.HandleFunc("GET /api/v1/categories/code/{code}/subcategories", authMiddleware(r.categoryHandler.GetSubcategoriesByCode))
	routes.HandleFunc("GET /api/v1/categories/parents", authMiddleware(r.categoryHandler.GetParentCategories))
	routes.HandleFunc("GET /api/v1/categories/details/{id}", authMiddleware(r.categoryHandler.GetCategoryWithDetails))
	routes.HandleFunc("GET /api/v1/categories/code/{code}/details", authMiddleware(r.categoryHandler.GetCategoryWithDetailsByCode))
	routes.HandleFunc("GET /api/v1/categories/validate-code/{code}", authMiddleware(r.categoryHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/work-areas", authMiddleware(r.workAreaHandler.Create))
	routes.HandleFunc("PUT /api/v1/work-areas", authMiddleware(r.workAreaHandler.Update))
	routes.HandleFunc("GET /api/v1/work-areas/{id}", authMiddleware(r.workAreaHandler.GetById))
	routes.HandleFunc("GET /api/v1/work-areas", authMiddleware(r.workAreaHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/work-areas/{id}", authMiddleware(r.workAreaHandler.Delete))
	routes.HandleFunc("GET /api/v1/work-areas/validate-code/{code}", authMiddleware(r.workAreaHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/operations", authMiddleware(r.operationHandler.Create))
	routes.HandleFunc("PUT /api/v1/operations", authMiddleware(r.operationHandler.Update))
	routes.HandleFunc("GET /api/v1/operations/{id}", authMiddleware(r.operationHandler.GetById))
	routes.HandleFunc("GET /api/v1/operations", authMiddleware(r.operationHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/operations/{id}", authMiddleware(r.operationHandler.Delete))
	routes.HandleFunc("GET /api/v1/operations/validate-code/{code}", authMiddleware(r.operationHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/production-flows", authMiddleware(r.productionFlowHandler.Create))
	routes.HandleFunc("POST /api/v1/production-flows/with-activities", authMiddleware(r.productionFlowHandler.CreateWithActivities))
	routes.HandleFunc("PUT /api/v1/production-flows", authMiddleware(r.productionFlowHandler.Update))
	routes.HandleFunc("GET /api/v1/production-flows/{id}", authMiddleware(r.productionFlowHandler.GetById))
	routes.HandleFunc("GET /api/v1/production-flows", authMiddleware(r.productionFlowHandler.GetAll))
	routes.HandleFunc("DELETE /api/v1/production-flows/{id}", authMiddleware(r.productionFlowHandler.Delete))
	routes.HandleFunc("GET /api/v1/production-flows/activities/{id}", authMiddleware(r.productionFlowHandler.GetWithActivities))
	routes.HandleFunc("GET /api/v1/production-flows/validate-code/{code}", authMiddleware(r.productionFlowHandler.ValidateCode))

	routes.HandleFunc("POST /api/v1/auth/login", r.authHandler.Login)
	routes.HandleFunc("POST /api/v1/auth/logout", r.authHandler.Logout)
	routes.HandleFunc("GET /api/v1/auth/is_logged_in", authMiddleware(r.authHandler.IsLoggedIn))
}

func NewRouteConfig(
	http *http.ServeMux,
	log *logrus.Logger,
	middleware restMdlwr.HTTPMiddleware,
	userHandler userRest.UserHandler,
	authHandler authRest.AuthHandler,
	brandHandler brandRest.BrandHandler,
	categoryHandler categoryRest.CategoryHandler,
	workAreaHandler workAreaRest.WorkAreaHandler,
	operationHandler operationRest.OperationHandler,
	productionFlowHandler productionFlowRest.ProductionFlowHandler,
	measurementUnitHandler measurementUnitRest.MeasurementUnitHandler,
) RouteConfig {
	return &routeConfig{
		http:                   http,
		log:                    log,
		middlware:              middleware,
		userHandler:            userHandler,
		authHandler:            authHandler,
		brandHandler:           brandHandler,
		categoryHandler:        categoryHandler,
		workAreaHandler:        workAreaHandler,
		operationHandler:       operationHandler,
		productionFlowHandler:  productionFlowHandler,
		measurementUnitHandler: measurementUnitHandler,
	}
}

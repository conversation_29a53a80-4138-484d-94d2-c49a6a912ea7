package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
)

type productResult struct {
	ID                string     `json:"id"`
	Name              string     `json:"name"`
	ImageURL          *string    `json:"image_url"`
	CommercialName    string     `json:"commercial_name"`
	Code              string     `json:"code"`
	SKUCode           string     `json:"sku_code"`
	MeasurementUnitID string     `json:"measurement_unit_id"`
	CategoryID        string     `json:"category_id"`
	BrandID           string     `json:"brand_id"`
	State             string     `json:"state"`
	Description       *string    `json:"description"`
	CanBeSold         bool       `json:"can_be_sold"`
	CanBePurchased    bool       `json:"can_be_purchased"`
	CostPrice         *float64   `json:"cost_price"`
	CreatedAt         *time.Time `json:"created_at"`
	UpdatedAt         *time.Time `json:"updated_at"`
	DeletedAt         *time.Time `json:"deleted_at"`
}

func productToResult(product *model.Product) productResult {
	return productResult{
		ID:                product.ID,
		Name:              product.Name,
		ImageURL:          product.ImageURL,
		CommercialName:    product.CommercialName,
		Code:              product.Code,
		SKUCode:           product.SKUCode,
		MeasurementUnitID: product.MeasurementUnitID,
		CategoryID:        product.CategoryID,
		BrandID:           product.BrandID,
		State:             product.State,
		Description:       product.Description,
		CanBeSold:         product.CanBeSold,
		CanBePurchased:    product.CanBePurchased,
		CostPrice:         product.CostPrice,
		CreatedAt:         product.CreatedAt,
		UpdatedAt:         product.UpdatedAt,
		DeletedAt:         product.DeletedAt,
	}
}

type productCreate struct {
	Name              string   `json:"name" validate:"required"`
	ImageURL          *string  `json:"image_url"`
	CommercialName    string   `json:"commercial_name" validate:"required"`
	Code              string   `json:"code" validate:"required"`
	SKUCode           string   `json:"sku_code" validate:"required"`
	MeasurementUnitID string   `json:"measurement_unit_id" validate:"required"`
	CategoryID        string   `json:"category_id" validate:"required"`
	BrandID           string   `json:"brand_id" validate:"required"`
	State             string   `json:"state"`
	Description       *string  `json:"description"`
	CanBeSold         bool     `json:"can_be_sold"`
	CanBePurchased    bool     `json:"can_be_purchased"`
	CostPrice         *float64 `json:"cost_price"`
}

func productCreateToModel(dto productCreate) model.ProductCreate {
	return model.ProductCreate{
		Name:              dto.Name,
		ImageURL:          dto.ImageURL,
		CommercialName:    dto.CommercialName,
		Code:              dto.Code,
		SKUCode:           dto.SKUCode,
		MeasurementUnitID: dto.MeasurementUnitID,
		CategoryID:        dto.CategoryID,
		BrandID:           dto.BrandID,
		State:             dto.State,
		Description:       dto.Description,
		CanBeSold:         dto.CanBeSold,
		CanBePurchased:    dto.CanBePurchased,
		CostPrice:         dto.CostPrice,
	}
}

meta {
  name: Create Production Flow with Activities
  type: http
  seq: 7
}

post {
  url: {{url}}/api/v1/production-flows/with-activities
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "code": "COMPLETE_FLOW_001",
    "name": "Complete Production Flow with Activities",
    "activities": [
      {
        "work_area_id": "{{work_area_id}}",
        "operation_id": "{{operation_id}}",
        "index_number": 1
      },
      {
        "work_area_id": "{{work_area_id}}",
        "operation_id": "{{operation_id}}",
        "index_number": 2
      }
    ]
  }
}

tests {
  test("should return 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("should return production flow ID", function() {
    expect(res.getBody()).to.be.a('string');
    bru.setEnvVar("production_flow_with_activities_id", res.getBody());
  });
}

docs {
  This endpoint creates a production flow along with its activities in a single transaction.
  
  Requirements:
  - work_area_id and operation_id must exist in the database
  - index_number must be unique within the production flow
  - activities array must contain at least one activity
  
  The operation is atomic - if any part fails, the entire operation is rolled back.
}

meta {
  name: Create Product
  type: http
  seq: 1
}

post {
  url: {{url}}/api/v1/products
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Laptop Dell Inspiron 15",
    "commercial_name": "Dell Inspiron 15 3000",
    "code": "DELL-INS-15",
    "sku_code": "SKU-DELL-001",
    "measurement_unit_id": "01JDQR8XQZM8YBVN9KPQR2ST3U",
    "category_id": "01JDQR8XQZM8YBVN9KPQR2ST3V",
    "brand_id": "01JDQR8XQZM8YBVN9KPQR2ST3W",
    "state": "active",
    "description": "High-performance laptop for business and personal use",
    "can_be_sold": true,
    "can_be_purchased": true,
    "cost_price": 599.99,
    "image_url": "https://example.com/images/dell-inspiron-15.jpg"
  }
}

tests {
  test("should return 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("should return product ID", function() {
    expect(res.getBody()).to.be.a('string');
  });
}

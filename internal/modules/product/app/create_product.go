package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/oklog/ulid/v2"
)

// Create implements model.ProductUsecase.
func (p *productUsecase) Create(ctx context.Context, product model.ProductCreate) (string, error) {
	// Check if name already exists
	nameExists, err := p.repo.CountByProp(ctx, "name", product.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if product name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.ProductConflictNamef("Product name already exists", nil, nil)
	}

	// Check if code already exists
	codeExists, err := p.repo.CountByProp(ctx, "code", product.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if product code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.ProductConflictCodef("Product code already exists", nil, nil)
	}

	// Check if SKU code already exists
	skuExists, err := p.repo.CountByProp(ctx, "sku_code", product.SKUCode)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if product SKU code exists", err, nil)
	}

	if skuExists > 0 {
		return "", model.ProductConflictSKUf("Product SKU code already exists", nil, nil)
	}

	// Set default state if not provided
	state := product.State
	if state == "" {
		state = "active"
	}

	// Generate ID and create product
	id := ulid.Make().String()
	productEntity := model.Product{
		ID:                id,
		Name:              product.Name,
		ImageURL:          product.ImageURL,
		CommercialName:    product.CommercialName,
		Code:              product.Code,
		SKUCode:           product.SKUCode,
		MeasurementUnitID: product.MeasurementUnitID,
		CategoryID:        product.CategoryID,
		BrandID:           product.BrandID,
		State:             state,
		Description:       product.Description,
		CanBeSold:         product.CanBeSold,
		CanBePurchased:    product.CanBePurchased,
		CostPrice:         product.CostPrice,
	}

	err = p.repo.Create(ctx, productEntity)
	if err != nil {
		return "", err
	}

	return id, nil
}

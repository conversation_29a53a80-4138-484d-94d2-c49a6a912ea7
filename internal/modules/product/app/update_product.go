package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.ProductUsecase.
func (p *productUsecase) Update(ctx context.Context, product model.ProductUpdate) error {
	// Check if the product exists
	existingProduct, err := p.repo.GetByProp(ctx, "id", product.ID)
	if err != nil {
		return err
	}

	// Check if name already exists (excluding current product)
	nameExists, err := p.repo.CountByProp(ctx, "name", product.Name)
	if err != nil {
		return utils.InternalErrorf("Failed to check if product name exists", err, nil)
	}

	if nameExists > 0 && existingProduct.Name != product.Name {
		return model.ProductConflictNamef("Product name already exists", nil, nil)
	}

	// Check if code already exists (excluding current product)
	codeExists, err := p.repo.CountByProp(ctx, "code", product.Code)
	if err != nil {
		return utils.InternalErrorf("Failed to check if product code exists", err, nil)
	}

	if codeExists > 0 && existingProduct.Code != product.Code {
		return model.ProductConflictCodef("Product code already exists", nil, nil)
	}

	// Check if SKU code already exists (excluding current product)
	skuExists, err := p.repo.CountByProp(ctx, "sku_code", product.SKUCode)
	if err != nil {
		return utils.InternalErrorf("Failed to check if product SKU code exists", err, nil)
	}

	if skuExists > 0 && existingProduct.SKUCode != product.SKUCode {
		return model.ProductConflictSKUf("Product SKU code already exists", nil, nil)
	}

	// Set default state if not provided
	state := product.State
	if state == "" {
		state = "active"
	}

	// Update product
	productEntity := model.Product{
		ID:                product.ID,
		Name:              product.Name,
		ImageURL:          product.ImageURL,
		CommercialName:    product.CommercialName,
		Code:              product.Code,
		SKUCode:           product.SKUCode,
		MeasurementUnitID: product.MeasurementUnitID,
		CategoryID:        product.CategoryID,
		BrandID:           product.BrandID,
		State:             state,
		Description:       product.Description,
		CanBeSold:         product.CanBeSold,
		CanBePurchased:    product.CanBePurchased,
		CostPrice:         product.CostPrice,
	}

	return p.repo.Update(ctx, productEntity)
}

package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Create implements ProductHandler.
func (p *productHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[productCreate](w, r, p.validator)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		return
	}

	id, err := p.useCase.Create(ctx, productCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to create product")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}

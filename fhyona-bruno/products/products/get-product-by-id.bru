meta {
  name: Get Product By ID
  type: http
  seq: 3
}

get {
  url: {{url}}/api/v1/products/01JDQR8XQZM8YBVN9KPQR2ST3X
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  test("should return 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  test("should return product object", function() {
    expect(res.getBody()).to.be.an('object');
    expect(res.getBody().id).to.be.a('string');
    expect(res.getBody().name).to.be.a('string');
    expect(res.getBody().code).to.be.a('string');
  });
}

package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Update implements CategoryHandler.
func (c *categoryHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[model.CategoryUpdate](w, r, c.validator)
	if err != nil {
		utils.LogErr(ctx, c.log, err)
		return
	}

	if err := c.useCase.Update(ctx, *req); err != nil {
		utils.LogErr(ctx, c.log, err)
		respErrHandler(w, r, err, "Failed to update category")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}

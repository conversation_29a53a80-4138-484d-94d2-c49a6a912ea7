package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Update implements ProductHandler.
func (p *productHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[model.ProductUpdate](w, r, p.validator)
	if err != nil {
		utils.LogErr(ctx, p.log, err)
		return
	}

	if err := p.useCase.Update(ctx, *req); err != nil {
		utils.LogErr(ctx, p.log, err)
		respErrHandler(w, r, err, "Failed to update product")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}

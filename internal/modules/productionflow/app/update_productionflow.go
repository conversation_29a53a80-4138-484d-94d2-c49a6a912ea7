package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/productionflow/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.ProductionFlowUsecase.
func (p *productionFlowUsecase) Update(ctx context.Context, productionFlow model.ProductionFlowUpdate) error {
	// Check if production flow exists
	existing, err := p.repo.GetByProp(ctx, "id", productionFlow.ID)
	if err != nil {
		return err
	}

	// Check if code already exists (excluding current production flow)
	if existing.Code != productionFlow.Code {
		codeExists, err := p.repo.CountByProp(ctx, "code", productionFlow.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if production flow code exists", err, nil)
		}

		if codeExists > 0 {
			return model.ProductionFlowConflictCodef("Production flow code already exists", nil, nil)
		}
	}

	// Check if name already exists (excluding current production flow)
	if existing.Name != productionFlow.Name {
		nameExists, err := p.repo.CountByProp(ctx, "name", productionFlow.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if production flow name exists", err, nil)
		}

		if nameExists > 0 {
			return model.ProductionFlowConflictNamef("Production flow name already exists", nil, nil)
		}
	}

	updatedProductionFlow := model.ProductionFlow{
		ID:   productionFlow.ID,
		Code: productionFlow.Code,
		Name: productionFlow.Name,
	}

	return p.repo.Update(ctx, updatedProductionFlow)
}
